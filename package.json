{"name": "instalikepoc", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.13", "expo-dev-client": "^5.2.2", "expo-status-bar": "~2.2.3", "expo-system-ui": "^5.0.9", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.26.0", "react-native-reanimated": "^3.18.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}