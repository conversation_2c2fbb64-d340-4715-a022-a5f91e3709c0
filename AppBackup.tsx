import React from "react";
import { StyleSheet, Text, View, Image } from "react-native";
import { GestureHandlerRootView, GestureDetector, Gesture } from "react-native-gesture-handler";
import Animated, { useSharedValue, useAnimatedStyle, SharedValue } from "react-native-reanimated";

// === Component for drag, scale and rotate ===
const IntractiveGesture = ({
  children,
  elementId,
  initialScale = 1,
  minScale = 0.5,
  maxScale = 3,
  initialRotation = 0,
  activeElementId,
  highestZIndex,
  enableTwoFingerPan = false,
}: {
  children: React.ReactNode;
  elementId: string;
  initialScale?: number;
  minScale?: number;
  maxScale?: number;
  initialRotation?: number;
  activeElementId: SharedValue<string | null>;
  highestZIndex?: SharedValue<number>;
  enableTwoFingerPan?: boolean;
}) => {
  const scale = useSharedValue(initialScale);
  const savedScale = useSharedValue(initialScale);

  const rotation = useSharedValue(initialRotation);
  const savedRotation = useSharedValue(initialRotation);

  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const savedTranslateX = useSharedValue(0);
  const savedTranslateY = useSharedValue(0);

  const zIndex = useSharedValue(0);

  const initialDistance = useSharedValue(0);
  const initialAngle = useSharedValue(0);
  const anchorPoint = useSharedValue({ x: 0, y: 0 });
  const initialTouchPoint = useSharedValue({ x: 0, y: 0 });
  const isTransforming = useSharedValue(false);

  const activeGestureCount = useSharedValue(0);

  const calculateDistance = (x1: number, y1: number, x2: number, y2: number) => {
    "worklet";
    return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
  };

  const calculateAngle = (x1: number, y1: number, x2: number, y2: number) => {
    "worklet";
    return Math.atan2(y2 - y1, x2 - x1) * (180 / Math.PI);
  };

  const normalizeAngle = (angle: number) => {
    "worklet";
    while (angle > 180) angle -= 360;
    while (angle < -180) angle += 360;
    return angle;
  };

  const onGestureStart = () => {
    "worklet";
    if (activeGestureCount.value === 0) {
      if (activeElementId.value === null) {
        activeElementId.value = elementId;
        if (highestZIndex) {
          highestZIndex.value += 1;
          zIndex.value = highestZIndex.value;
        }
      }
    }
    activeGestureCount.value += 1;
  };

  const onGestureEnd = () => {
    "worklet";
    activeGestureCount.value -= 1;
    if (activeGestureCount.value === 0) {
      if (activeElementId.value === elementId) {
        activeElementId.value = null;
      }
    }
  };

  const panGesture = Gesture.Pan()
    .minPointers(2)
    .maxPointers(2)
    .onStart(onGestureStart)
    .onUpdate((e) => {
      translateX.value = savedTranslateX.value + e.translationX;
      translateY.value = savedTranslateY.value + e.translationY;
    })
    .onEnd(() => {
      "worklet";
      savedTranslateX.value = translateX.value;
      savedTranslateY.value = translateY.value;
      onGestureEnd();
    });

  const pinchGesture = Gesture.Pinch()
    .onStart(onGestureStart)
    .onUpdate((e) => {
      scale.value = Math.min(Math.max(savedScale.value * e.scale, minScale), maxScale);
    })
    .onEnd(() => {
      "worklet";
      savedScale.value = scale.value;
      onGestureEnd();
    });

  const rotateGesture = Gesture.Rotation()
    .onStart(onGestureStart)
    .onUpdate((e) => {
      rotation.value = savedRotation.value + (e.rotation * 180) / Math.PI;
    })
    .onEnd(() => {
      "worklet";
      savedRotation.value = rotation.value;
      onGestureEnd();
    });

  const composedGesture = Gesture.Simultaneous(panGesture, pinchGesture, rotateGesture);

  const gesture = Gesture.Manual()
    .onTouchesDown((event, stateManager) => {
      const touches = event.allTouches;

      if (activeElementId.value === null || activeElementId.value === elementId) {
        activeElementId.value = elementId;
        if (highestZIndex) {
          highestZIndex.value += 1;
          zIndex.value = highestZIndex.value;
        }

        if (touches.length === 1 && !enableTwoFingerPan) {
          isTransforming.value = false;

          anchorPoint.value = {
            x: touches[0].absoluteX,
            y: touches[0].absoluteY,
          };

          initialTouchPoint.value = {
            x: touches[0].absoluteX,
            y: touches[0].absoluteY,
          };

          savedTranslateX.value = translateX.value;
          savedTranslateY.value = translateY.value;
        } else if (touches.length === 2) {
          isTransforming.value = true;

          const firstTouch = touches[0];
          const secondTouch = touches[1];

          if (enableTwoFingerPan) {
            initialTouchPoint.value = {
              x: (firstTouch.absoluteX + secondTouch.absoluteX) / 2,
              y: (firstTouch.absoluteY + secondTouch.absoluteY) / 2,
            };
            savedTranslateX.value = translateX.value;
            savedTranslateY.value = translateY.value;
          }

          anchorPoint.value = {
            x: firstTouch.absoluteX,
            y: firstTouch.absoluteY,
          };

          initialDistance.value = calculateDistance(
            firstTouch.absoluteX,
            firstTouch.absoluteY,
            secondTouch.absoluteX,
            secondTouch.absoluteY
          );

          initialAngle.value = calculateAngle(
            firstTouch.absoluteX,
            firstTouch.absoluteY,
            secondTouch.absoluteX,
            secondTouch.absoluteY
          );

          savedScale.value = scale.value;
          savedRotation.value = rotation.value;

          stateManager.activate();
        }
      }
    })
    .onTouchesMove((event) => {
      if (activeElementId.value !== elementId) return;

      const touches = event.allTouches;

      if (touches.length === 1 && !isTransforming.value && !enableTwoFingerPan) {
        const deltaX = touches[0].absoluteX - initialTouchPoint.value.x;
        const deltaY = touches[0].absoluteY - initialTouchPoint.value.y;

        translateX.value = savedTranslateX.value + deltaX;
        translateY.value = savedTranslateY.value + deltaY;
      } else if (touches.length === 2) {
        if (enableTwoFingerPan) {
          const currentMidpointX = (touches[0].absoluteX + touches[1].absoluteX) / 2;
          const currentMidpointY = (touches[0].absoluteY + touches[1].absoluteY) / 2;
          const deltaX = currentMidpointX - initialTouchPoint.value.x;
          const deltaY = currentMidpointY - initialTouchPoint.value.y;

          translateX.value = savedTranslateX.value + deltaX;
          translateY.value = savedTranslateY.value + deltaY;
        }

        if (isTransforming.value) {
          const firstTouch = touches[0];
          const secondTouch = touches[1];

          const currentDistance = calculateDistance(
            firstTouch.absoluteX,
            firstTouch.absoluteY,
            secondTouch.absoluteX,
            secondTouch.absoluteY
          );

          const scaleRatio = currentDistance / initialDistance.value;
          scale.value = Math.min(Math.max(savedScale.value * scaleRatio, minScale), maxScale);

          const currentAngle = calculateAngle(
            firstTouch.absoluteX,
            firstTouch.absoluteY,
            secondTouch.absoluteX,
            secondTouch.absoluteY
          );

          const angleDifference = normalizeAngle(currentAngle - initialAngle.value);
          rotation.value = savedRotation.value + angleDifference;
        }
      }
    })
    .onTouchesUp((event, stateManager) => {
      if (enableTwoFingerPan && event.numberOfTouches < 2) {
        activeElementId.value = null;
        stateManager.end();
        return;
      }
      isTransforming.value = false;

      savedTranslateX.value = translateX.value;
      savedTranslateY.value = translateY.value;
      savedScale.value = scale.value;
      savedRotation.value = rotation.value;
      initialDistance.value = 0;
      initialAngle.value = 0;

      activeElementId.value = null;

      stateManager.end();

      if (event.numberOfTouches === 1 && event.allTouches?.length > 0) {
        initialTouchPoint.value = {
          x: event.allTouches[0].absoluteX,
          y: event.allTouches[0].absoluteY,
        };
      }
    });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
      { rotate: `${rotation.value}deg` },
    ],
    zIndex: zIndex.value,
  }));

  return (
    <GestureDetector gesture={enableTwoFingerPan ? composedGesture : gesture}>
      <Animated.View style={[animatedStyle]}>{children}</Animated.View>
    </GestureDetector>
  );
};

// === Main screen ===
const Example = () => {
  const activeElementId = useSharedValue<string | null>(null);
  const highestZIndex = useSharedValue(0);

  return (
    <GestureHandlerRootView style={styles.container}>
      <IntractiveGesture
        elementId='background'
        activeElementId={activeElementId}
        enableTwoFingerPan={true}
        minScale={0.3}
        maxScale={10}
      >
        <Image source={require("./assets/bg.jpg")} style={styles.backgroundImage} />
      </IntractiveGesture>

      <View style={styles.canvas}>
        <IntractiveGesture elementId='element-1' activeElementId={activeElementId} highestZIndex={highestZIndex}>
          <Text style={styles.textElement}>Touch to move/scale/rotate</Text>
        </IntractiveGesture>

        <IntractiveGesture elementId='element-2' activeElementId={activeElementId} highestZIndex={highestZIndex}>
          <View style={styles.imageElement}>
            <Text style={styles.imageText}>📷</Text>
            <Text style={styles.imageSubtext}>No Jump Element</Text>
          </View>
        </IntractiveGesture>

        <IntractiveGesture elementId='element-3' activeElementId={activeElementId} highestZIndex={highestZIndex}>
          <View style={styles.stickerElement}>
            <Text style={styles.stickerText}>✨ Fixed Movement</Text>
          </View>
        </IntractiveGesture>
      </View>
    </GestureHandlerRootView>
  );
};

export default Example;

// === Styles ===
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  canvas: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  elementContainer: {
    // margin: 20,
    // padding: 4,
  },
  textElement: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    padding: 15,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    overflow: "hidden",
  },
  imageElement: {
    width: 120,
    height: 120,
    backgroundColor: "#007AFF",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  imageText: {
    fontSize: 30,
    marginBottom: 5,
  },
  imageSubtext: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 12,
  },
  stickerElement: {
    backgroundColor: "#FF6B6B",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
  },
  stickerText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
  },
  backgroundImage: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    resizeMode: "cover",
  },
  instructions: {
    position: "absolute",
    bottom: 50,
    left: 20,
    right: 20,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    padding: 15,
    borderRadius: 10,
  },
  instructionText: {
    color: "#fff",
    fontSize: 14,
    marginBottom: 2,
  },
});
