import React, { useState } from "react";
import { StyleSheet, View, Image, Text } from "react-native";
import { GestureDetector, Gesture, GestureHandlerRootView } from "react-native-gesture-handler";
import Animated, { useSharedValue, useAnimatedStyle, runOnJS } from "react-native-reanimated";

export default function App() {
  // Track the active element and highest z-index
  const activeElementId = useSharedValue<string | null>(null);
  const highestZIndex = useSharedValue(3);

  // Shared values for each element's transformations
  const element1Scale = useSharedValue(1);
  const element1SavedScale = useSharedValue(1);
  const element1Rotation = useSharedValue(0);
  const element1SavedRotation = useSharedValue(0);
  const element1TranslateX = useSharedValue(50);
  const element1TranslateY = useSharedValue(100);

  const element2Scale = useSharedValue(1);
  const element2SavedScale = useSharedValue(1);
  const element2Rotation = useSharedValue(0);
  const element2SavedRotation = useSharedValue(0);
  const element2TranslateX = useSharedValue(150);
  const element2TranslateY = useSharedValue(300);

  const element3Scale = useSharedValue(1);
  const element3SavedScale = useSharedValue(1);
  const element3Rotation = useSharedValue(0);
  const element3SavedRotation = useSharedValue(0);
  const element3TranslateX = useSharedValue(200);
  const element3TranslateY = useSharedValue(500);

  // State to track z-index of elements
  const [elements, setElements] = useState([
    { id: "element1", zIndex: 1 },
    { id: "element2", zIndex: 2 },
    { id: "element3", zIndex: 3 },
  ]);

  // Function to update element z-index
  const bringToFront = (id) => {
    setElements((prev) => {
      const newElements = [...prev];
      const index = newElements.findIndex((el) => el.id === id);
      if (index !== -1) {
        highestZIndex.value += 1;
        newElements[index].zIndex = highestZIndex.value;
      }
      return newElements;
    });
  };

  // Find the topmost element (highest z-index)
  const getTopmostElement = () => {
    return elements.reduce((topmost, current) => 
      current.zIndex > topmost.zIndex ? current : topmost
    );
  };

  // Create a pinch gesture that affects only the topmost element
  const pinchGesture = Gesture.Pinch()
    .onUpdate((e) => {
      const topmost = getTopmostElement();
      if (topmost.id === "element1") {
        element1Scale.value = element1SavedScale.value * e.scale;
      } else if (topmost.id === "element2") {
        element2Scale.value = element2SavedScale.value * e.scale;
      } else if (topmost.id === "element3") {
        element3Scale.value = element3SavedScale.value * e.scale;
      }
    })
    .onEnd(() => {
      const topmost = getTopmostElement();
      if (topmost.id === "element1") {
        element1SavedScale.value = element1Scale.value;
      } else if (topmost.id === "element2") {
        element2SavedScale.value = element2Scale.value;
      } else if (topmost.id === "element3") {
        element3SavedScale.value = element3Scale.value;
      }
    });

  // Create a rotation gesture that affects only the topmost element
  const rotationGesture = Gesture.Rotation()
    .onUpdate((e) => {
      const topmost = getTopmostElement();
      if (topmost.id === "element1") {
        element1Rotation.value = element1SavedRotation.value + e.rotation * (180 / Math.PI);
      } else if (topmost.id === "element2") {
        element2Rotation.value = element2SavedRotation.value + e.rotation * (180 / Math.PI);
      } else if (topmost.id === "element3") {
        element3Rotation.value = element3SavedRotation.value + e.rotation * (180 / Math.PI);
      }
    })
    .onEnd(() => {
      const topmost = getTopmostElement();
      if (topmost.id === "element1") {
        element1SavedRotation.value = element1Rotation.value;
      } else if (topmost.id === "element2") {
        element2SavedRotation.value = element2Rotation.value;
      } else if (topmost.id === "element3") {
        element3SavedRotation.value = element3Rotation.value;
      }
    });

  // Combine all gestures
  const composedGesture = Gesture.Simultaneous(pinchGesture, rotationGesture);

  // Helper to get element z-index
  const getElementZIndex = (id) => {
    const element = elements.find((el) => el.id === id);
    return element ? element.zIndex : 0;
  };

  // Animated styles for each element
  const element1Style = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: element1TranslateX.value },
        { translateY: element1TranslateY.value },
        { scale: element1Scale.value },
        { rotateZ: `${element1Rotation.value}deg` },
      ],
      zIndex: getElementZIndex("element1"),
      borderColor: activeElementId.value === "element1" ? "#00ff00" : "transparent",
    };
  });

  const element2Style = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: element2TranslateX.value },
        { translateY: element2TranslateY.value },
        { scale: element2Scale.value },
        { rotateZ: `${element2Rotation.value}deg` },
      ],
      zIndex: getElementZIndex("element2"),
      borderColor: activeElementId.value === "element2" ? "#00ff00" : "transparent",
    };
  });

  const element3Style = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: element3TranslateX.value },
        { translateY: element3TranslateY.value },
        { scale: element3Scale.value },
        { rotateZ: `${element3Rotation.value}deg` },
      ],
      zIndex: getElementZIndex("element3"),
      borderColor: activeElementId.value === "element3" ? "#00ff00" : "transparent",
    };
  });

  // Pan gestures for each element
  const element1Pan = Gesture.Pan()
    .onStart(() => {
      activeElementId.value = "element1";
      runOnJS(bringToFront)("element1");
    })
    .onUpdate((e) => {
      element1TranslateX.value += e.changeX;
      element1TranslateY.value += e.changeY;
    });

  const element2Pan = Gesture.Pan()
    .onStart(() => {
      activeElementId.value = "element2";
      runOnJS(bringToFront)("element2");
    })
    .onUpdate((e) => {
      element2TranslateX.value += e.changeX;
      element2TranslateY.value += e.changeY;
    });

  const element3Pan = Gesture.Pan()
    .onStart(() => {
      activeElementId.value = "element3";
      runOnJS(bringToFront)("element3");
    })
    .onUpdate((e) => {
      element3TranslateX.value += e.changeX;
      element3TranslateY.value += e.changeY;
    });

  // Tap gestures for each element
  const element1Tap = Gesture.Tap().onStart(() => {
    activeElementId.value = "element1";
    runOnJS(bringToFront)("element1");
  });

  const element2Tap = Gesture.Tap().onStart(() => {
    activeElementId.value = "element2";
    runOnJS(bringToFront)("element2");
  });

  const element3Tap = Gesture.Tap().onStart(() => {
    activeElementId.value = "element3";
    runOnJS(bringToFront)("element3");
  });

  // Combined gestures for each element
  const gesture1 = Gesture.Exclusive(element1Tap, element1Pan);
  const gesture2 = Gesture.Exclusive(element2Tap, element2Pan);
  const gesture3 = Gesture.Exclusive(element3Tap, element3Pan);

  return (
    <GestureHandlerRootView style={styles.container}>
      {/* Elements */}
      <GestureDetector gesture={gesture1}>
        <Animated.View style={[styles.elementContainer, element1Style]}>
          <Image source={{ uri: "https://picsum.photos/500/500" }} style={styles.image} resizeMode='cover' />
        </Animated.View>
      </GestureDetector>

      <GestureDetector gesture={gesture2}>
        <Animated.View style={[styles.elementContainer, element2Style]}>
          <View style={styles.textContainer}>
            <Text style={styles.text}>Drag Me!</Text>
          </View>
        </Animated.View>
      </GestureDetector>

      <GestureDetector gesture={gesture3}>
        <Animated.View style={[styles.elementContainer, element3Style]}>
          <View style={styles.emojiContainer}>
            <Text style={styles.emoji}>🔥</Text>
          </View>
        </Animated.View>
      </GestureDetector>

      {/* Global gesture detector for pinch and rotation - affects topmost element */}
      <GestureDetector gesture={composedGesture}>
        <View style={styles.globalGestureContainer} />
      </GestureDetector>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#121212",
  },
  globalGestureContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "transparent",
  },
  elementContainer: {
    position: "absolute",
    borderWidth: 2,
  },
  image: {
    width: 200,
    height: 200,
    borderRadius: 20,
  },
  textContainer: {
    width: 150,
    height: 80,
    backgroundColor: "#3498db",
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  text: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
  emojiContainer: {
    width: 100,
    height: 100,
    backgroundColor: "#e74c3c",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  emoji: {
    fontSize: 40,
  },
});
